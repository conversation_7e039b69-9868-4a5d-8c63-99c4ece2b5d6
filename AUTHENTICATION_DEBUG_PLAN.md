# Stream Upload Authentication Debug Plan

## Current Issue

From the console logs in `TempConsoleLogs.md`, we can see:

1. ✅ **User info is correctly passed**: `{userId: '58422', userName: 'R17 C', channelId: '11533'}`
2. ✅ **Stream upload is selected**: `Final decision for tiptube: Stream`
3. ❌ **Backend authentication fails**: `Failed to create TipTube upload URL: Authentication error`
4. ✅ **R2 fallback works**: Upload succeeds via R2 after Stream fails

## Root Cause Analysis

The issue is at the backend API level:
- Frontend is sending correct user info and auth token
- Backend `/api/direct-upload/tiptube` endpoint returns 400 status with "Authentication error"
- This suggests the `Auth.verifyToken` middleware is rejecting the token

## Debugging Steps Implemented

### 1. Enhanced Frontend Debugging

**DirectUploadService.ts:**
- Added detailed token logging (first 20 characters)
- Added AsyncStorage key inspection
- Added token validation test with simple endpoint
- Added request/response debugging

**UnifiedUploadService.ts:**
- Added ApiService authentication test
- This will compare DirectUploadService vs ApiService authentication

### 2. Token Retrieval Verification

Both services now use identical token retrieval logic:
```typescript
let token = await AsyncStorage.getItem('accessToken');
if (!token) {
  token = await AsyncStorage.getItem('@auth_token');
}
```

### 3. Authentication Test

Added test call to `/api/getcategories` to verify token validity before attempting Stream upload.

## Expected Debug Output

When you run the upload again, you should see:

```
[DirectUpload] accessToken from AsyncStorage: eyJhbGciOiJIUzI1NiIsInR5...
[DirectUpload] Final token to use: eyJhbGciOiJIUzI1NiIsInR5...
[DirectUpload] All auth-related keys in AsyncStorage: ['accessToken', '@auth_token', 'user', ...]
[DirectUpload] Testing token with simple endpoint...
[DirectUpload] Token test response status: 200
[DirectUpload] Token test successful - token is valid
[UnifiedUpload] Testing authentication with ApiService...
[UnifiedUpload] ApiService auth test result: {...}
```

## Possible Issues & Solutions

### Issue 1: Token Expired
**Symptoms:** Token test fails with 401
**Solution:** App needs to refresh token or re-authenticate

### Issue 2: Wrong Token Format
**Symptoms:** Token exists but backend rejects it
**Solution:** Check token format and Auth.verifyToken implementation

### Issue 3: Backend Service Missing
**Symptoms:** 404 or 500 errors
**Solution:** Verify backend DirectUploadService is deployed

### Issue 4: CORS or Network Issues
**Symptoms:** Network errors or CORS failures
**Solution:** Check network connectivity and CORS configuration

## Next Steps Based on Debug Output

### If Token Test Succeeds but Direct Upload Fails:
- Issue is specific to `/api/direct-upload/tiptube` endpoint
- Check backend DirectUploadService implementation
- Verify Auth.verifyToken middleware on that specific route

### If Token Test Fails:
- Issue is with authentication token itself
- Need to refresh token or re-authenticate user
- Check token storage and retrieval logic

### If ApiService Test Succeeds but DirectUploadService Fails:
- Issue is with DirectUploadService implementation
- Compare request headers and format between services
- Use ApiService for direct upload calls instead

## Backend Verification

The backend endpoints exist in `adtipback/routes/api-routes.js`:
```javascript
router.post('/direct-upload/tiptube', Auth.verifyToken, async (req, res) => {
  // Implementation exists
});
```

## Testing Instructions

1. **Run Upload**: Try uploading a video through TipTube
2. **Monitor Console**: Look for the debug output above
3. **Check Results**: 
   - If token test succeeds → Backend issue
   - If token test fails → Frontend auth issue
   - If ApiService succeeds → Use ApiService approach

## Fallback Plan

If authentication continues to fail:
1. **Temporary Solution**: Continue using R2 uploads (working)
2. **Long-term Fix**: Resolve authentication issue for Stream uploads
3. **Alternative**: Implement Stream uploads through different authentication method

The compression fallback we implemented ensures uploads work regardless of the Stream authentication issue, so users can continue uploading while we debug this.
