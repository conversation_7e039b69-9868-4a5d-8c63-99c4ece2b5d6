[useGuestShortsQuery] Total transformed shorts: 20
ApiService.ts:284 📥 API RESPONSE: {method: 'GET', url: '/api/getshots/58422?page=1&limit=10', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/getshots/58422?page=1&limit=10', status: 200, statusText: undefined, headers: {…}, data: {…}, timestamp: '2025-07-26T08:45:38.584Z'}baseURL: "http://*************:7082"data: data: Array(20)0: {id: 4404, name: 'machar.love news funny video ', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/820be45b1b5f66180cd8d24768d567b7/manifest/video.m3u8', video_desciption: 'machar.love news funny 🤣😂🤣 video ', total_views: 0, total_likes: -3, createdby: 47414, video_Thumbnail: 'https://theadtip.in/image/1000005487.png', stream_video_id: '820be45b1b5f66180cd8d24768d567b7', …}1: {id: 3057, name: 'shadow monarch', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/0299686d90b682f51c5f7568ac5079a8/manifest/video.m3u8', video_desciption: '#shadow monarch,#sung jinwoo,#arise,#solo leveling', total_views: 0, total_likes: 1, createdby: 46610, video_Thumbnail: 'https://theadtip.in/image/1000010553.jpg', stream_video_id: '0299686d90b682f51c5f7568ac5079a8', …}2: {id: 4448, name: 'poem', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/f8f748ea1c2c8aef9575ebe7a261b108/manifest/video.m3u8', video_desciption: 'undefined', total_views: 0, total_likes: -18, createdby: 14767, video_Thumbnail: 'undefined', stream_video_id: 'f8f748ea1c2c8aef9575ebe7a261b108', …}3: {id: 4528, name: 'videos ', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/af7d1bd5e3c991f3fca3dd709fb9e31e/manifest/video.m3u8', video_desciption: 'undefined', total_views: 0, total_likes: -5, createdby: 38375, video_Thumbnail: 'undefined', stream_video_id: 'af7d1bd5e3c991f3fca3dd709fb9e31e', …}4: {id: 4612, name: 'Mr bist car collection budget expensive car collection', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/4923fc4809753f8ef678932404f2e573/manifest/video.m3u8', video_desciption: 'undefined', total_views: 0, total_likes: -7, createdby: 55492, video_Thumbnail: 'undefined', stream_video_id: '4923fc4809753f8ef678932404f2e573', …}5: {id: 4616, name: 'Mr bist  expensive car collection', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/4923fc4809753f8ef678932404f2e573/manifest/video.m3u8', video_desciption: 'undefined', total_views: 0, total_likes: -3, createdby: 55492, video_Thumbnail: 'undefined', stream_video_id: '4923fc4809753f8ef678932404f2e573', …}6: {id: 807, name: '#valuable one ', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/fc17d02ead19d8637e534df9c3e3a986/manifest/video.m3u8', video_desciption: "we hope it's good", total_views: 0, total_likes: 37, createdby: 39288, video_Thumbnail: 'https://theadtip.in/image/1000033962.jpg', stream_video_id: 'fc17d02ead19d8637e534df9c3e3a986', …}7: {id: 939, name: 'gulab jamun', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/f1450525b8fd4c7bcbf72d1f6b923b5f/manifest/video.m3u8', video_desciption: 'gulab jamun ', total_views: 0, total_likes: 55, createdby: 40586, video_Thumbnail: 'https://theadtip.in/image/1000023688.jpg', stream_video_id: 'f1450525b8fd4c7bcbf72d1f6b923b5f', …}8: {id: 1063, name: 'tillu square ', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/dc191601074e85cf6a7c2ed446bef60b/manifest/video.m3u8', video_desciption: 'atluntadhi manathoni .......!!', total_views: 0, total_likes: 25, createdby: 40919, video_Thumbnail: 'https://theadtip.in/image/1000058546.jpg', stream_video_id: 'dc191601074e85cf6a7c2ed446bef60b', …}9: {id: 1339, name: 'follow for more edits🖤🥀👀', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/07a6e7f84e0f179e25f2c8b10c6bb438/manifest/video.m3u8', video_desciption: '#follow', total_views: 0, total_likes: 25, createdby: 41409, video_Thumbnail: 'https://theadtip.in/image/1000029665.jpg', stream_video_id: '07a6e7f84e0f179e25f2c8b10c6bb438', …}10: {id: 1611, name: 'Cute Cats', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/a771c950d8a17c57e334c228be45735b/manifest/video.m3u8', video_desciption: '#cutecat #catvideo #cats #cat #catlife #catlovers #catshorts', total_views: 0, total_likes: 17, createdby: 40596, video_Thumbnail: 'https://theadtip.in/image/1000002222.png', stream_video_id: 'a771c950d8a17c57e334c228be45735b', …}11: {id: 1691, name: 'khaleja movie song edit | ', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/e57a5b62e09f6e95795cb090098dd5dc/manifest/video.m3u8', video_desciption: '#editor #editing #maheshbabu #anushka\n', total_views: 0, total_likes: 24, createdby: 42285, video_Thumbnail: 'https://theadtip.in/image/1000000631.jpg', stream_video_id: 'e57a5b62e09f6e95795cb090098dd5dc', …}12: {id: 4493, name: 'break up 💔', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/1171f3542f683b5bc0ab324ee4a0935b/manifest/video.m3u8', video_desciption: 'undefined', total_views: 0, total_likes: -4, createdby: 52751, video_Thumbnail: 'undefined', stream_video_id: '1171f3542f683b5bc0ab324ee4a0935b', …}13: {id: 4613, name: 'Mr bist  expensive car collection', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/4923fc4809753f8ef678932404f2e573/manifest/video.m3u8', video_desciption: 'undefined', total_views: 0, total_likes: -2, createdby: 55492, video_Thumbnail: 'undefined', stream_video_id: '4923fc4809753f8ef678932404f2e573', …}14: {id: 552, name: 'True words 💯', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/e4e000bf571d214d68b0df7a530d8845/manifest/video.m3u8', video_desciption: 'time we fall.” — ...\n“Life is either a daring adventure or nothing.” —', total_views: 0, total_likes: 46, createdby: 39123, video_Thumbnail: 'https://theadtip.in/image/1000021743.jpg', stream_video_id: 'e4e000bf571d214d68b0df7a530d8845', …}15: {id: 2227, name: '#Old songs ', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/03df6af4b97961919c55e66b71111db4/manifest/video.m3u8', video_desciption: 'this is my first short video\n', total_views: 0, total_likes: 11, createdby: 45402, video_Thumbnail: 'https://theadtip.in/image/1000023864.jpg', stream_video_id: '03df6af4b97961919c55e66b71111db4', …}16: {id: 2231, name: '#old video ', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/03df6af4b97961919c55e66b71111db4/manifest/video.m3u8', video_desciption: 'this is my first short video \n', total_views: 0, total_likes: 23, createdby: 45402, video_Thumbnail: 'https://theadtip.in/image/1000023864.jpg', stream_video_id: '03df6af4b97961919c55e66b71111db4', …}17: {id: 1016, name: 'enjoy nature ', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/6275cb18ead94e07e331b416740aa324/manifest/video.m3u8', video_desciption: 'support 💙', total_views: 0, total_likes: 23, createdby: 39026, video_Thumbnail: 'https://theadtip.in/image/1000022188.jpg', stream_video_id: '6275cb18ead94e07e331b416740aa324', …}18: {id: 1340, name: 'follow for more edits🖤🥀👀', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/07a6e7f84e0f179e25f2c8b10c6bb438/manifest/video.m3u8', video_desciption: '#follow', total_views: 0, total_likes: 18, createdby: 41409, video_Thumbnail: 'https://theadtip.in/image/1000029665.jpg', stream_video_id: '07a6e7f84e0f179e25f2c8b10c6bb438', …}19: {id: 2999, name: 'cars 🚘', category_id: 0, video_link: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/1ba4e3366b8b1e0d8ecc3042e4e2a807/manifest/video.m3u8', video_desciption: '#viral #viralreels #viralvideos #viralpost #views\n#comments #share #instalike #instagood #instagram\n#instadaily #thar #thar4x4 #tharlover #réel #drifts #power#100klikes #100kview #100kfollower#maroc #100kshare#monster #offroading #motivation #mahindara #as #1760', total_views: 0, total_likes: -17, createdby: 46394, video_Thumbnail: 'https://theadtip.in/image/1000008784.jpg', stream_video_id: '1ba4e3366b8b1e0d8ecc3042e4e2a807', …}length: 20[[Prototype]]: Array(0)message: "Shot fetch successfully."status: 200[[Prototype]]: ObjectfullURL: "http://*************:7082/api/getshots/58422?page=1&limit=10"headers: {access-control-allow-credentials: 'true', access-control-allow-headers: 'Origin, X-Requested-With, Content-Type, Accept', access-control-allow-methods: 'GET, POST, PUT, DELETE', access-control-allow-origin: '*', connection: 'keep-alive', content-length: '16921', content-security-policy: "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", content-type: 'application/json; charset=utf-8', cross-origin-embedder-policy: 'require-corp', cross-origin-opener-policy: 'same-origin', …}method: "GET"status: 200statusText: undefinedtimestamp: "2025-07-26T08:45:38.584Z"url: "/api/getshots/58422?page=1&limit=10"[[Prototype]]: Object
useShortsQuery.ts:105 [TanStack] Successfully mapped 20 shorts for page 1
VideoPlaybackService.ts:241 [VideoPlaybackService] Stream playback support check: {videoId: '4404', streamVideoId: undefined, streamStatus: undefined, streamReadyAt: undefined, supports: false}
EnhancedShortCard.tsx:437 [EnhancedShortCard] Video debug info: {videoId: '4404', hasVideoLink: true, videoLinkValid: false, hasStreamId: false, streamIdValid: false, streamStatus: undefined, streamReady: false, hasStreamReadyAt: false, supportsStream: false, adaptiveManifestUrl: undefined}
VideoPlaybackService.ts:137 [VideoPlaybackService] Using R2 fallback: {videoId: '4404', videoUrl: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/820be45b1b5f66180cd8d24768d567b7/manifest/video.m3u8', quality: 'auto'}
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/820be45b1b5f66180cd8d24768d567b7/manifest/video.m3u8
VideoPlaybackService.ts:241 [VideoPlaybackService] Stream playback support check: {videoId: '3057', streamVideoId: undefined, streamStatus: undefined, streamReadyAt: undefined, supports: false}
EnhancedShortCard.tsx:437 [EnhancedShortCard] Video debug info: {videoId: '3057', hasVideoLink: true, videoLinkValid: false, hasStreamId: false, streamIdValid: false, streamStatus: undefined, streamReady: false, hasStreamReadyAt: false, supportsStream: false, adaptiveManifestUrl: undefined}
VideoPlaybackService.ts:137 [VideoPlaybackService] Using R2 fallback: {videoId: '3057', videoUrl: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/0299686d90b682f51c5f7568ac5079a8/manifest/video.m3u8', quality: 'auto'}
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/0299686d90b682f51c5f7568ac5079a8/manifest/video.m3u8
ModernRewardPopup.tsx:35 🎁 [ModernRewardPopup] Rendered with props: {visible: false, isPremium: false, earnedAmount: 0}
VideoPlaybackService.ts:241 [VideoPlaybackService] Stream playback support check: {videoId: '4448', streamVideoId: undefined, streamStatus: undefined, streamReadyAt: undefined, supports: false}
EnhancedShortCard.tsx:437 [EnhancedShortCard] Video debug info: {videoId: '4448', hasVideoLink: true, videoLinkValid: false, hasStreamId: false, streamIdValid: false, streamStatus: undefined, streamReady: false, hasStreamReadyAt: false, supportsStream: false, adaptiveManifestUrl: undefined}
VideoPlaybackService.ts:137 [VideoPlaybackService] Using R2 fallback: {videoId: '4448', videoUrl: 'https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/f8f748ea1c2c8aef9575ebe7a261b108/manifest/video.m3u8', quality: 'auto'}
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/f8f748ea1c2c8aef9575ebe7a261b108/manifest/video.m3u8
RewardedAdComponent.tsx:38 Rewarded ad loaded successfully
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/820be45b1b5f66180cd8d24768d567b7/manifest/video.m3u8
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/0299686d90b682f51c5f7568ac5079a8/manifest/video.m3u8
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/f8f748ea1c2c8aef9575ebe7a261b108/manifest/video.m3u8
ModernRewardPopup.tsx:35 🎁 [ModernRewardPopup] Rendered with props: {visible: false, isPremium: false, earnedAmount: 0}
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/0299686d90b682f51c5f7568ac5079a8/manifest/video.m3u8
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/f8f748ea1c2c8aef9575ebe7a261b108/manifest/video.m3u8
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://customer-cnqr7kkcyczlbymi.cloudflarestream.com/820be45b1b5f66180cd8d24768d567b7/manifest/video.m3u8
RectangleAdComponent.tsx:61 🔄 [RectangleAd] Auto-rotating to next ad network
AdRotationService.ts:71 🔄 [AdRotation] Switched to PubScale (rotation #2)
AdRotationService.ts:93 📱 [AdRotation] Next PubScale rectangle ad: /***********,***********/com.adtip.app.adtip_app.Mrec0.1752230666
RectangleAdComponent.tsx:61 🔄 [RectangleAd] Auto-rotating to next ad network
AdRotationService.ts:71 🔄 [AdRotation] Switched to Business Collaboration (rotation #3)
AdRotationService.ts:93 📱 [AdRotation] Next Business Collaboration rectangle ad: /***********,***********/com.adtip.app.adtip_app.Mrec0.175092925